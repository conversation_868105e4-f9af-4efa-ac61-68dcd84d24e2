# 公共组件使用指南

本文档详细介绍了项目中使用的两个核心组件库：`eem-base` 和 `cet-common` 的使用规范和适用范围。

## 一、EEM-BASE 组件库

### 1.1 CustomElSelect (自定义选择器)

**组件名称：** `CustomElSelect` 或 `customElSelect`

**使用规范：**
```vue
<CustomElSelect 
  prefix_in="选择类型" 
  popover_in="这是一个提示信息"
  v-model="selectedValue"
>
  <el-option label="选项1" value="1"></el-option>
  <el-option label="选项2" value="2"></el-option>
</CustomElSelect>
```

**属性说明：**
- `prefix_in`: String - 前缀标签文本
- `popover_in`: String - 悬停提示内容（可选）
- 继承 ElementUI Select 的所有属性和事件

**适用场景：**
- 需要带前缀标签的下拉选择器
- 需要问号提示图标的选择器
- 自定义样式的选择器（带边框、圆角等）

### 1.2 CustomElDatePicker (自定义日期选择器)

**组件名称：** `CustomElDatePicker`

**使用规范：**
```vue
<CustomElDatePicker 
  prefix_in="选择日期" 
  v-model="dateValue"
  type="date"
/>
```

**属性说明：**
- `prefix_in`: String - 前缀标签文本
- 继承 ElementUI DatePicker 的所有属性和事件

**适用场景：**
- 需要带前缀标签的日期选择器
- 自定义样式的日期选择器

### 1.3 CustomSteps (自定义步骤条)

**组件名称：** `customSteps`

**使用规范：**
```vue
<customSteps 
  :active="currentStep" 
  :steps="stepList"
/>
```

**属性说明：**
- `steps`: Array - 步骤数据（必需）
  - 每项包含：`title` (必需), `description` (可选)
- `active`: Number - 当前激活步骤索引

**数据格式：**
```javascript
stepList: [
  { title: '第一步', description: '填写基本信息' },
  { title: '第二步', description: '确认信息' },
  { title: '第三步', description: '完成' }
]
```

**适用场景：**
- 多步骤流程展示
- 向导式操作界面
- 进度指示器

### 1.4 UploadDialog (上传对话框)

**组件名称：** `UploadDialog`

**使用规范：**
```vue
<UploadDialog
  :openTrigger_in="openTrigger"
  :closeTrigger_in="closeTrigger"
  :extensionNameList_in="['.xlsx', '.xls']"
  dialogTitle="批量导入"
  :maxFlinkCount="1000"
  @uploadFile="handleUpload"
  @download="handleDownload"
/>
```

**属性说明：**
- `extensionNameList_in`: Array - 支持的文件扩展名列表（必需）
- `openTrigger_in`: Number - 打开触发器
- `closeTrigger_in`: Number - 关闭触发器
- `hideDownload`: Boolean - 是否隐藏下载模板按钮
- `dialogTitle`: String - 对话框标题
- `downloadPermission`: String - 下载权限
- `maxFlinkCount`: Number - 最大导入数据量

**事件说明：**
- `@uploadFile`: 文件上传事件
- `@download`: 模板下载事件

**适用场景：**
- 文件上传功能
- 批量数据导入
- 模板下载功能

### 1.5 工具函数

**数字精度处理：**
```javascript
// 已扩展到 Number.prototype
let num = 3.14159;
console.log(num.toFixed2(2)); // "3.14"
```

## 二、CET-COMMON 组件库

### 2.1 CetButton (按钮组件)

**组件名称：** `CetButton`

**使用规范：**
```vue
<CetButton
  :visible_in="true"
  :disable_in="false"
  title="确定"
  type="primary"
  @statusTrigger_out="handleClick"
/>
```

**属性说明：**
- `visible_in`: Boolean - 是否显示
- `disable_in`: Boolean - 是否禁用
- `title`: String - 按钮文本
- `size`: String - 尺寸，默认 'small'

**事件说明：**
- `@statusTrigger_out`: 点击时触发，返回时间戳

**适用场景：**
- 统一样式的按钮
- 需要状态控制的按钮

### 2.2 CetDialog (对话框组件)

**组件名称：** `CetDialog`

**使用规范：**
```vue
<CetDialog
  :openTrigger_in="openTrigger"
  :closeTrigger_in="closeTrigger"
  title="对话框标题"
  width="800px"
>
  <div>对话框内容</div>
  <template #footer>
    <CetButton title="取消" @statusTrigger_out="handleCancel"/>
    <CetButton title="确定" type="primary" @statusTrigger_out="handleConfirm"/>
  </template>
</CetDialog>
```

**属性说明：**
- `openTrigger_in`: Number - 打开触发器
- `closeTrigger_in`: Number - 关闭触发器
- `width`: String - 宽度，默认 '960px'
- `closeOnClickModal`: Boolean - 点击遮罩关闭
- `isDraggable`: Boolean - 是否可拖拽（通过全局配置控制）

**适用场景：**
- 模态对话框
- 弹窗表单
- 信息展示窗口

### 2.3 CetTable (表格组件)

**组件名称：** `CetTable`

**使用规范：**
```vue
<CetTable
  :tableData="tableData"
  :showPagination="true"
  :totalCount="total"
  :currentPage="currentPage"
  :pageSize="pageSize"
  @handleCurrentPageChange="handlePageChange"
  @handleSizeChange="handleSizeChange"
>
  <el-table-column prop="name" label="姓名"></el-table-column>
  <el-table-column prop="age" label="年龄"></el-table-column>
</CetTable>
```

**主要功能：**
- 内置分页器
- 支持排序、筛选
- 支持行选择
- 继承 ElementUI Table 的所有功能

**适用场景：**
- 数据列表展示
- 复杂表格操作
- 带分页的数据表格

### 2.4 CetForm (表单组件)

**组件名称：** `CetForm`

**使用规范：**
```vue
<CetForm
  :data="formData"
  dataMode="static"
  queryMode="trigger"
  :dataConfig="dataConfig"
>
  <el-form-item label="姓名" prop="name">
    <el-input v-model="formData.name"></el-input>
  </el-form-item>
</CetForm>
```

**属性说明：**
- `dataMode`: String - 数据获取模式（backendInterface/component/static）
- `queryMode`: String - 查询模式（trigger/diff）
- `dataConfig`: Object - 数据绑定配置
- `data`: Object - 表单数据

**适用场景：**
- 复杂表单处理
- 数据绑定表单
- 动态表单生成

### 2.5 CetDateSelect (日期选择组件)

**组件名称：** `CetDateSelect`

**使用规范：**
```vue
<CetDateSelect
  :typeList="['day', 'week', 'month']"
  layout="button"
  :showButton="true"
  @dateChange="handleDateChange"
/>
```

**主要功能：**
- 支持日、周、月、季、年选择
- 支持按钮和下拉两种布局模式
- 内置前后切换按钮

**适用场景：**
- 时间范围选择
- 统计报表时间筛选
- 多种时间粒度选择

### 2.6 CetTree (树形组件)

**组件名称：** `CetTree`

**使用规范：**
```vue
<CetTree
  :inputData_in="treeData"
  :showFilter="true"
  :selectNode="selectedNode"
  @currentChange="handleNodeClick"
  @check="handleNodeCheck"
/>
```

**主要功能：**
- 支持搜索过滤
- 支持节点选择和勾选
- 继承 ElementUI Tree 的功能

**适用场景：**
- 层级数据展示
- 组织架构树
- 分类选择器

### 2.7 CetSelectTree (树形选择器)

**组件名称：** `CetSelectTree`

**使用规范：**
```vue
<CetSelectTree
  v-model="selectedValue"
  :data="treeData"
  :multiple="false"
  :clearable="true"
/>
```

**主要功能：**
- 结合 Select 和 Tree 的功能
- 支持单选和多选
- 支持搜索过滤

**适用场景：**
- 层级数据选择
- 下拉树形选择器

### 2.8 其他组件

- **CetIcon**: 图标组件
- **CetTabs**: 标签页组件
- **CetTransfer**: 穿梭框组件
- **CetAside**: 侧边栏组件
- **CetGiantTree**: 大数据量树形组件
- **CetSimpleSelect**: 简单选择器
- **CetZtree**: 基于 zTree 的树形组件

## 三、全局配置

### 3.1 组件库安装配置

在 main.js 中的配置示例：

```javascript
import CetCommon from "cet-common";
import { CustomElSelect, CustomElDatePicker } from "eem-base/components";

// 注册 eem-base 组件
Vue.component("CustomElDatePicker", CustomElDatePicker);
Vue.component("customElSelect", CustomElSelect);
Vue.component("CustomElSelect", CustomElSelect);

// 安装 cet-common
Vue.use(CetCommon, {
  api: customApi,
  CetDialog: {
    isDraggable: false
  },
  CetTable: {
    isColumsHeaderDoLayout: true
  }
});
```

### 3.2 默认配置说明

通过 `defaultSettings` 可以配置组件默认行为：

- `CetDialog.isDraggable`: 对话框是否可拖拽
- `CetTable.isColumsHeaderDoLayout`: 表格列头是否重新布局
- `CetGiantTree.isAutoCancelSelected`: 大树是否自动取消选择
- `CetDateSelect.isClosedEnd`: 日期选择是否闭合结束

## 四、最佳实践

### 4.1 命名规范
- 组件名使用 PascalCase 或 camelCase
- 属性名使用 camelCase 或 snake_case（根据组件要求）
- 事件名使用 camelCase

### 4.2 使用建议
1. 优先使用组件库提供的组件，保持界面一致性
2. 合理使用触发器模式进行组件间通信
3. 充分利用组件的插槽功能进行自定义
4. 注意组件的生命周期和数据绑定

### 4.3 注意事项
- 部分组件依赖全局的 API 配置
- 某些组件需要特定的 CSS 变量支持
- 触发器属性通常使用时间戳进行状态管理

---

**版本信息：**
- eem-base: v1.0.1
- cet-common: v1.7.8

**更新日期：** 2025-07-08
